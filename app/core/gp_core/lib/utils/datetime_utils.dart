import 'package:gp_core/core.dart';

class DateTimeUtils {
  const DateTimeUtils._();

  static const DateTimeUtils instance = DateTimeUtils._();

  static const String shortDatePattern = "hh:mm";
  static const String datePattern_1 = "dd-MM-yyyy";
  static const String datePattern_2 = "dd/MM";
  static const String datePattern_3 = "dd/MM/yy";

  static const String timeSpacer = "\u2022";

  String convertDateToStringDefault(int milliseconds) {
    return DateFormat(shortDatePattern)
        .format(DateTime.fromMillisecondsSinceEpoch(milliseconds * 1000));
  }

  String convertDateToString(DateTime dateTime,
      {String datePattern = shortDatePattern}) {
    return DateFormat(datePattern).format(dateTime);
  }

  String convertDateToTime(DateTime? pickedTime) {
    if (pickedTime == null) {
      return LocaleKeys.task_datetimePicker_timePickerTitle.tr;
    }
    final dateFormat = DateFormat('h:mm a', 'en');
    return dateFormat.format(pickedTime);
  }

  DateTime utc(DateTime dateTime) {
    return dateTime.toUtc();
    // return dateTime = DateTime.utc(dateTime.year, dateTime.month, dateTime.day,
    //     dateTime.hour, dateTime.minute, dateTime.second).subtract(dateTime.timeZoneOffset);
  }

  DateTime _dateNow() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  bool _isYesterday(DateTime input) {
    final yesterday = _dateNow().subtract(const Duration(days: 1));

    return DateTime(input.year, input.month, input.day).isSameDate(yesterday);
  }

  bool isToday(int milliseconds) {
    DateTime input = DateTime.fromMillisecondsSinceEpoch(milliseconds);

    return DateTime(input.year, input.month, input.day).isSameDate(_dateNow());
  }

  bool _isTomorrow(DateTime input) {
    final tomorrow = _dateNow().add(const Duration(days: 1));

    return DateTime(input.year, input.month, input.day).isSameDate(tomorrow);
  }

  bool isInPast(int milliseconds) {
    DateTime input = DateTime.fromMillisecondsSinceEpoch(milliseconds);

    return input.isBefore(DateTime.now());
  }

  bool checkYear(DateTime input) {
    return input.isSameYear(_dateNow());
  }

  String getDueDateStr(int dueDateMilliseconds, {bool isShowOnlyDate = false}) {
    final DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(dueDateMilliseconds);

    String dateStr = "";
    String timeStr = convertDateToTime(dateTime);

    if (_isYesterday(dateTime)) {
      dateStr = LocaleKeys.time_yesterday.tr;
    } else if (isToday(dueDateMilliseconds)) {
      dateStr = LocaleKeys.time_today.tr;
    } else if (_isTomorrow(dateTime)) {
      dateStr = LocaleKeys.time_tomorrow.tr;
    } else if (checkYear(dateTime)) {
      dateStr = DateFormat(datePattern_2).format(dateTime);
    } else {
      dateStr = DateFormat(datePattern_3).format(dateTime);
    }

    if (isShowOnlyDate) {
      return dateStr;
    }
    // return dạng 23h59' • Hôm nay
    return timeStr.isNotEmpty ? '$timeStr $timeSpacer $dateStr' : dateStr;
  }

  /// Kiểm tra xem `startDate` và `endDate` có trong khoảng `yearAgo` hay không
  /// 
  /// <br> e.g: user nhập 01/01/2020 - 01/01/2021 
  /// <br> result -> true mặc dù day difference: 366, hoặc thậm chí 367.
  static bool isSameYearAgo(
    DateTime startDate,
    DateTime endDate, {
    int yearAgo = 1,
  }) {
    final days = 365 * yearAgo;
    final bool isDifference = endDate.difference(startDate).inDays <= days;

    final isSameDay = startDate.day == endDate.day;
    final isSameMonth = startDate.month == endDate.month;
    final isSameYear = startDate.year == endDate.year - yearAgo;

    return ((isSameDay && isSameMonth && isSameYear) || isDifference);
  }

  static DateTime toIsoDateTime(String isoDateTimeStr) {
    List<String> untilSplitteds = isoDateTimeStr.split("T");

    String yyyymmdd = untilSplitteds.first;
    final int year = int.parse(yyyymmdd.substring(0, 4));
    final int month = int.parse(yyyymmdd.substring(4, 6));
    final int day = int.parse(yyyymmdd.substring(6, 8));

    String hhmmss = untilSplitteds.last;
    final int hh = int.parse(hhmmss.substring(0, 2));
    final int mm = int.parse(hhmmss.substring(2, 4));
    final int ss = int.parse(hhmmss.substring(4, 6));

    return DateTime(year, month, day, hh, mm, ss);
  }

  static int weekDayStringToInt(String day) {
    switch (day) {
      case "MO":
        return DateTime.monday;
      case "TU":
        return DateTime.tuesday;
      case "WE":
        return DateTime.wednesday;
      case "TH":
        return DateTime.thursday;
      case "FR":
        return DateTime.friday;
      case "SA":
        return DateTime.saturday;
      case "SU":
        return DateTime.sunday;
      default:
        return 0;
    }
  }

  static String intToWeekDayString(int day) {
    switch (day) {
      case 1:
        return "MO";
      case 2:
        return "TU";
      case 3:
        return "WE";
      case 4:
        return "TH";
      case 5:
        return "FR";
      case 6:
        return "SA";
      case 7:
        return "SU";
      default:
        return "";
    }
  }

  bool isValidTimeFormat(String time) {
    RegExp timeRegex = RegExp(r'^(0[1-9]|1[0-2]):[0-5][0-9]\s(AM|PM|am|pm)$');
    return timeRegex.hasMatch(time);
  }

  bool isValid24hTimeFormat(String time) {
    RegExp timeRegex = RegExp(r'^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(time);
  }

  /// input: strTime = "09:00 PM" --> output = datetime.date + time (21:00:00:0000)
  DateTime? convertStringTimeToDateTimeToday(
      String? strTime, DateTime dateTime) {
    if (strTime == null || !isValidTimeFormat(strTime)) {
      return null;
    }
    final parsedDateTime = DateFormat('hh:mm a').parse(strTime);
    return DateTime(dateTime.year, dateTime.month, dateTime.day,
        parsedDateTime.hour, parsedDateTime.minute);
  }

  /// input: strTime = "21:00" --> output = DateTime today with time (21:00:00:0000)
  DateTime? convert24hStringTimeToDateTimeToday(String? strTime) {
    if (strTime == null || !isValid24hTimeFormat(strTime)) {
      return null;
    }
    final dateTime = DateTime.now();
    final parsedDateTime = DateFormat('HH:mm').parse(strTime);
    return DateTime(dateTime.year, dateTime.month, dateTime.day,
        parsedDateTime.hour, parsedDateTime.minute);
  }

  static String convertSecondToDayHourMinute(int seconds) {
    final roundedMinute = (seconds / 60).round();
    final day = roundedMinute ~/ 1440;
    final hour = (roundedMinute % 1440) ~/ 60;
    final minute = roundedMinute % 60;
    var str = '';
    if (day > 0) {
      str += '$day ${LocaleKeys.time_short_day.tr} ';
    }
    if (hour > 0) {
      str += '$hour${LocaleKeys.time_short_hour.tr}';
    }
    if (minute > 0) {
      str +=
          ' ${minute < 10 ? '0' : ''}$minute${LocaleKeys.time_short_minute.tr}';
    }
    if (seconds < 30) {
      str = '0${LocaleKeys.time_short_minute.tr}';
    }
    return str;
  }

  String getFirstDateNextMonth(String dateFormat) {
    final now = DateTime.now();
    if (now.month == 12) {
      return DateFormat(dateFormat).format(DateTime(now.year + 1, 1, 1));
    } else {
      return DateFormat(dateFormat).format(DateTime(now.year, now.month + 1, 1));
    }
  }
}

extension DateOnlyCompare on DateTime {
  bool isSameDate(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }

  bool isSameYear(DateTime other) {
    return year == other.year;
  }

  bool hasTime() {
    return hour != 0 && minute != 0;
  }

  /*
    Tính ngày X xuất hiện lần Y trong tháng.
    Ví dụ Friday, Sep 16, 2022: dayAppearInAMonth = 3
  */
  int get dayAppearInAMonth => weekOfMonth; //day ~/ 7 + 1;

  String get weekDayStr {
    if (weekday == DateTime.monday) {
      return "MO";
    } else if (weekday == DateTime.tuesday) {
      return "TU";
    } else if (weekday == DateTime.wednesday) {
      return "WE";
    } else if (weekday == DateTime.thursday) {
      return "TH";
    } else if (weekday == DateTime.friday) {
      return "FR";
    } else if (weekday == DateTime.saturday) {
      return "SA";
    } else if (weekday == DateTime.sunday) {
      return "SU";
    }
    return "";
  }

  /*
    String datetime dạng iso có dạng 20220916T000000Z
  */
  String get isoStr {
    return "$year${_addZeroPrefix(month)}${_addZeroPrefix(day)}"
        "T"
        "${_addZeroPrefix(hour)}${_addZeroPrefix(minute)}${_addZeroPrefix(second)}"
        "Z";
  }

  String _addZeroPrefix(int number) {
    return number < 10 ? "0$number" : "$number";
  }

  /*
    Tạo datetime từ `date` và `time`
  */
  DateTime mergeTime(DateTime time) {
    return DateTime(
      year,
      month,
      day,
      time.hour,
      time.minute,
      time.second,
      time.millisecond,
      time.microsecond,
    );
  }
}
