import 'dart:convert';

import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:decimal/decimal.dart';
import 'package:decimal/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';
import 'package:math_parser_bigdecimal/math_parser.dart';

import '../../../../../domain/entity/workflow/workflow_form.entity.dart';
import '../base/base_input_text_params.dart';
import 'input_currency.entity.dart';

const _kKeyParam = 'gp_param';
const _kThousandSymbol = ',';

enum TicketCreateInputNumberType {
  inputTypeNumber,
  inputTypeCurrency,
  inputTypeFormula,
}

final class FormulaEntity {
  FormulaEntity({
    required this.id,
    required this.input,
  });

  final int id;
  Decimal input;
}

/// Dùng chung cho các loại input number `TicketCreateInputNumberType`
class TicketCreateInputNumberParams extends BaseInputTextParams {
  TicketCreateInputNumberParams({
    required this.workFlowFormFieldEntity,
    required super.formFieldType,
    required super.id,
    required this.title,
    required this.keepDecimalPlaces,
    required this.inputNumberType,
    required this.isRequired,
    required super.formIndex,
    this.enabled = true,
    this.isThousandSeparator = true,
    this.showClearBtn = true,
    this.hint,
    this.subTitle,
    this.unitStr,
    this.textValue,
    this.btnDoneStr,
    this.maxLines,
    this.maxLength,
    this.minValue,
    this.maxValue,
    // this.textInputType = TextInputType.numberWithOptions(decimal: true),
    this.trailing,
    this.currencies,
    // this.validator = validatorNumber,
    this.validator,
    super.permissions,
  }) : textInputType = TextInputType.number;

  // ---------- TypeCurrency ---------- \\
  factory TicketCreateInputNumberParams.fromWorkFlowFormFieldEntityTypeCurrency(
    WorkFlowFormFieldEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final option = entity.option;

    return TicketCreateInputNumberParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: entity,
      inputNumberType: TicketCreateInputNumberType.inputTypeCurrency,
      id: entity.id,
      formFieldType: entity.type,
      title: entity.title,
      hint: entity.hint,
      isRequired: entity.isRequired,
      unitStr: entity.option.unit,
      subTitle: entity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      enabled: true,
      //
      currencies: _currencyFromFormEntity(option),
      permissions: permissions,
    );
  }

  factory TicketCreateInputNumberParams.fromWorkFlowFieldValuesEntityTypeCurrency(
    WorkFlowFieldValuesEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final WorkFlowFormFieldEntity formFieldEntity = entity.info;
    final option = formFieldEntity.option;

    if (entity.value != null) {
      formFieldEntity.currentText = entity.value;
    }

    final ret = TicketCreateInputNumberParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: formFieldEntity,
      inputNumberType: TicketCreateInputNumberType.inputTypeCurrency,
      id: formFieldEntity.id,
      formFieldType: formFieldEntity.type,
      title: formFieldEntity.title,
      hint: formFieldEntity.hint,
      isRequired: formFieldEntity.isRequired,
      // currentText: entity.value,
      unitStr: formFieldEntity.option.unit,
      subTitle: formFieldEntity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      enabled: true,
      //
      currencies: _currencyFromFormEntity(option),
      permissions: permissions,
    );

    ret._updateSelectedCurrency(entity.unitId);

    return ret;
  }

  // ---------- TypeNumber ---------- \\
  factory TicketCreateInputNumberParams.fromWorkFlowFormFieldEntityTypeNumber(
    WorkFlowFormFieldEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final option = entity.option;

    return TicketCreateInputNumberParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: entity,
      inputNumberType: TicketCreateInputNumberType.inputTypeNumber,
      id: entity.id,
      formFieldType: entity.type,
      title: entity.title,
      hint: entity.hint,
      isRequired: entity.isRequired,
      unitStr: entity.option.unit,
      subTitle: entity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      enabled: true,
      //
      currencies: null,
      permissions: permissions,
    );
  }

  factory TicketCreateInputNumberParams.fromWorkFlowFieldValuesEntityTypeNumber(
    WorkFlowFieldValuesEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final WorkFlowFormFieldEntity formFieldEntity = entity.info;
    final option = formFieldEntity.option;
    if (entity.value != null) {
      formFieldEntity.currentText = entity.value;
    }

    return TicketCreateInputNumberParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: formFieldEntity,
      inputNumberType: TicketCreateInputNumberType.inputTypeNumber,
      id: formFieldEntity.id,
      formFieldType: formFieldEntity.type,
      title: formFieldEntity.title,
      hint: formFieldEntity.hint,
      isRequired: formFieldEntity.isRequired,
      // currentText: entity.value,
      unitStr: formFieldEntity.option.unit,
      subTitle: formFieldEntity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      enabled: true,
      //
      currencies: null,
      permissions: permissions,
    );
  }

  static List<InputCurrencyEntity>? _currencyFromFormEntity(
    WorkFlowFormOptionEntity option,
  ) {
    if (option.content.isEmpty) return null;

    final Map<String, dynamic> decoded = jsonDecode(option.content);
    final ret = <InputCurrencyEntity>[];

    for (var element in decoded.values) {
      ret.add(InputCurrencyEntity.fromJson(element));
    }

    return ret;
  }

  void _updateSelectedCurrency(dynamic unitId) {
    if (unitId == null) return;
    if (currencies == null || currencies?.isEmpty == true) return;

    for (var currency in currencies ?? []) {
      if (currency.id.toString() == unitId.toString()) {
        currentCurrency = currency;
      }
    }
    currentCurrency ??= currencies?.first;
  }

  final WorkFlowFormFieldEntity workFlowFormFieldEntity;
  final TicketCreateInputNumberType inputNumberType;

  final String title;
  final String? subTitle, hint, textValue, btnDoneStr, unitStr;

  final int? maxLines, maxLength;
  final double? minValue, maxValue;
  final int keepDecimalPlaces;
  final bool isThousandSeparator;
  bool showClearBtn;

  final TextInputType? textInputType;
  final String? trailing;

  final List<InputCurrencyEntity>? currencies;
  InputCurrencyEntity? currentCurrency;

  final String? Function(String? input)? validator;
  final bool isRequired, enabled;

  final ValueNotifier<bool> rxHasFocus = ValueNotifier(false);

  //
  TicketCreateInputFormulaParams? formulaParams;

  late final CurrencyTextInputFormatter format =
      CurrencyTextInputFormatter.currency(
    symbol: '',
    enableNegative: true,
    decimalDigits: keepDecimalPlaces,
  );

  late final DecimalFormatter decimalFormat =
      DecimalFormatter(NumberFormat.currency(
    symbol: '',
    decimalDigits: keepDecimalPlaces,
  ));

  Decimal parseCurrentValue(String text) {
    if (text.isEmpty || text == '-' || text == '.') return Decimal.zero;

    text = text.replaceAll(_kThousandSymbol, '');

    return Decimal.tryParse(text) ?? Decimal.zero;
  }

  String parseCurrentValueStr(String text) {
    final numValue = parseCurrentValue(text);
    // case setup workflow hiển thị 0 số thập phân nhưng đang trả về có số thập phân
    if (numValue.isInteger == false &&
        keepDecimalPlaces == 0 &&
        isThousandSeparator) {
      final scale = numValue.scale;
      final newDecimalFormat = DecimalFormatter(NumberFormat.currency(
        symbol: '',
        decimalDigits: scale,
      ));
      return newDecimalFormat.format(numValue);
    }

    return text.isEmpty
        ? ''
        : isThousandSeparator
            ? decimalFormat.format(numValue)
            : numValue.toStringAsFixed(keepDecimalPlaces);
  }

  String get displayCurrentCode {
    if (currentCurrency != null) {
      return currentCurrency?.code ?? '';
    }

    return '';
  }

  @override
  dynamic toTicketValueJson() {
    return workFlowFormFieldEntity.currentText;
  }

  @override
  Map<String, dynamic>? toTicketJson() {
    final fieldValue = toTicketValueJson();

    if (fieldValue == null) {
      return null;
    }

    return TicketCreateFormValueData(
      id: id,
      formFieldType: formFieldType,
      value: fieldValue,
      unitId: currentCurrency?.id,
    ).toJson();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketCreateInputNumberParams &&
        other.title == title &&
        other.subTitle == subTitle &&
        other.hint == hint &&
        other.textValue == textValue &&
        other.btnDoneStr == btnDoneStr &&
        other.unitStr == unitStr &&
        other.maxLines == maxLines &&
        other.maxLength == maxLength &&
        other.minValue == minValue &&
        other.maxValue == maxValue &&
        other.keepDecimalPlaces == keepDecimalPlaces &&
        other.isThousandSeparator == isThousandSeparator &&
        other.showClearBtn == showClearBtn &&
        other.trailing == trailing &&
        other.isRequired == isRequired &&
        other.enabled == enabled &&
        other.id == id;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      title,
      subTitle,
      hint,
      textValue,
      btnDoneStr,
      unitStr,
      maxLines,
      maxLength,
      minValue,
      maxValue,
      keepDecimalPlaces,
      isThousandSeparator,
      showClearBtn,
      trailing,
      isRequired,
      enabled,
      id,
    ]);
  }
}

final class TicketCreateInputFormulaParams
    extends TicketCreateInputNumberParams {
  TicketCreateInputFormulaParams({
    required super.workFlowFormFieldEntity,
    required super.id,
    required super.formFieldType,
    required super.title,
    required super.keepDecimalPlaces,
    required super.inputNumberType,
    required super.isRequired,
    required super.formIndex,
    super.enabled = true,
    super.isThousandSeparator = true,
    super.showClearBtn = true,
    super.hint,
    super.subTitle,
    super.unitStr,
    super.textValue,
    super.btnDoneStr,
    super.maxLines,
    super.maxLength,
    super.minValue,
    super.maxValue,
    super.trailing,
    super.currencies,
    super.validator,
    this.formula,
    this.formulaArgs,
    super.permissions,
  }) {
    _formulaStr = _formulaCheck(formula ?? '');

    if (!enabled) {
      showClearBtn = false;
    }

    if (hasFormula) {
      for (var i = 0; i < (formulaArgs?.length ?? 0); i++) {
        _formulaStr = _formulaStr.replaceAll('\$$i', '$_kKeyParam$i');
      }
    }
  }

  factory TicketCreateInputFormulaParams.fromWorkFlowFormFieldEntityTypeFormula(
    WorkFlowFormFieldEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final option = entity.option;

    return TicketCreateInputFormulaParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: entity,
      inputNumberType: TicketCreateInputNumberType.inputTypeFormula,
      id: entity.id,
      formFieldType: entity.type,
      title: entity.title,
      hint: entity.hint,
      isRequired: entity.isRequired,
      unitStr: entity.option.unit,
      subTitle: entity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      //
      currencies: null,
      formula: entity.option.formula,
      formulaArgs: entity.option.formulaArgs,
      enabled: false,
      permissions: permissions,
    );
  }

  factory TicketCreateInputFormulaParams.fromWorkFlowFieldValuesEntityTypeFormula(
    WorkFlowFieldValuesEntity entity, {
    WorkflowFormFieldPermissionEntity? permissions,
    required int formIndex,
  }) {
    final WorkFlowFormFieldEntity formFieldEntity = entity.info;
    final option = formFieldEntity.option;
    if (entity.value != null) {
      formFieldEntity.currentText = entity.value;
    }

    return TicketCreateInputFormulaParams(
      formIndex: formIndex,
      workFlowFormFieldEntity: formFieldEntity,
      inputNumberType: TicketCreateInputNumberType.inputTypeFormula,
      id: formFieldEntity.id,
      formFieldType: formFieldEntity.type,
      title: formFieldEntity.title,
      hint: formFieldEntity.hint,
      isRequired: formFieldEntity.isRequired,
      unitStr: formFieldEntity.option.unit,
      subTitle: formFieldEntity.isRequired ? '*' : '',
      keepDecimalPlaces: option.keepDecimalPlaces,
      isThousandSeparator: option.isThousandSeparator,
      minValue: option.minValue,
      maxValue: option.maxValue,
      //
      currencies: null,
      formula: formFieldEntity.option.formula,
      formulaArgs: formFieldEntity.option.formulaArgs,
      enabled: false,
      permissions: permissions,
    );
  }

  final String? formula;

  String _formulaStr = '';

  final List<int>? formulaArgs;
  final List<FormulaEntity> _formulaEntities = [];

  void Function(String text)? formulaCallback;

  bool get hasFormula =>
      formula?.isNotEmpty == true && formulaArgs?.isNotEmpty == true;

  void expressionNoParams() {
    try {
      final expression = MathNodeExpression.fromString(
        _formulaStr,
        variableNames: {},
      ).calc(
        const MathVariableValues({}),
      );

      String ret = expression.toStringAsFixed(keepDecimalPlaces);

      if (!isThousandSeparator) {
        formulaCallback?.call(ret);
      }

      ret = format.formatDouble(expression.toDouble());
      formulaCallback?.call(ret);
      workFlowFormFieldEntity.currentText = ret;
    } catch (e) {
      logDebug('expressionNoParams error -> $e');
    }
  }

  void initFormulaEntities(FormulaEntity entity) {
    _formulaEntities.add(entity);

    onFormulaTextChanged(entity);
  }

  String onFormulaTextChanged(FormulaEntity entity) {
    try {
      _formulaEntities.firstWhereOrNull((e) => e.id == entity.id)?.input =
          entity.input;

      final expression = MathNodeExpression.fromString(
        _formulaStr,
        variableNames: formulaSet,
      ).calc(
        MathVariableValues(formulaMap),
      );

      String ret = expression.toStringAsFixed(keepDecimalPlaces);

      if (!isThousandSeparator) {
        formulaCallback?.call(ret);
        return ret;
      }

      ret = decimalFormat.format(Decimal.parse(expression.toString()));
      formulaCallback?.call(ret);

      return ret;
    } catch (e) {
      logDebug('onFormulaTextChanged error -> $e');
    }

    return '';
  }

  Set<String> get formulaSet {
    final ret = <String>[];
    for (var i = 0; i < _formulaEntities.length; i++) {
      ret.addAll({'$_kKeyParam$i'});
    }

    return ret.toSet();
  }

  Map<String, Decimal> get formulaMap {
    final ret = <String, Decimal>{};

    for (var i = 0; i < _formulaEntities.length; i++) {
      ret.addAll({
        '$_kKeyParam$i': _formulaEntities[i].input,
      });
    }

    return ret;
  }

  void setOnFormulaListener(void Function(String text) callback) {
    formulaCallback = callback;
  }

  String _formulaCheck(String formula) {
    if (formula.isEmpty) {
      return '';
    }

    /// 01/08/2024: ToanNM
    /// còn case + 99, xử lý reg khá lằng nhằng, nên bỏ qua

    final splitters = formula.split(' ');
    List<String> ret = [];
    for (var element in splitters) {
      if (element.startsWith('.')) {
        final doubleValue =
            double.parse('0.${element.substring(1, element.length)}')
                .roundedPrecision(2);
        ret.add('$doubleValue');
      } else {
        ret.add(element);
      }
    }

    return ret.join(' ');
  }

  @override
  dynamic toTicketValueJson() {
    return workFlowFormFieldEntity.currentText;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketCreateInputFormulaParams &&
        other.formula == formula &&
        listEquals(other.formulaArgs, formulaArgs) &&
        other.id == id &&
        other.isRequired == isRequired;
  }

  @override
  int get hashCode {
    return Object.hash(
      formula,
      formulaArgs,
      id,
      isRequired,
    );
  }
}
