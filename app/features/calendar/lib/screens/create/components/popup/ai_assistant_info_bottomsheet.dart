import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class AIAssistantInfoBottomSheet extends StatelessWidget {
  const AIAssistantInfoBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Header with avatar and title
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Avatar
                    SizedBox(
                      width: 48,
                      height: 48,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: Image.asset(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Title
                    Text(
                      'Bạn đang sử dụng gói dùng thử dành cho Trợ lý cuộc họp',
                      style: textStyle(GPTypography.headingXLarge),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // Information list
                Column(
                  children: [
                    _buildInfoItem(
                      '👉',
                      'Thư ký AI đẳng cấp: ',
                      'lắng nghe từng ý, tóm tắt sắc gọn, chốt quyết định rõ ràng chỉ sau vài phút.',
                    ),
                    const SizedBox(height: 16),
                    _buildInfoItem(
                      '👉',
                      'Từ họp → hành: ',
                      'tự sinh biên bản chuẩn, danh sách việc cần làm, gán owner & deadline, theo dõi tiến độ tự động.',
                    ),
                    const SizedBox(height: 16),
                    _buildInfoItem(
                      '👉',
                      'Một trợ lý cho cả đội: ',
                      'nội dung đồng nhất, đầy đủ, không còn “ai nói gì” cả nhóm hiểu và chạy cùng một hướng.',
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Divider
          Divider(
            color: GPColor.lineTertiary,
            height: 1,
            thickness: 1,
            indent: 24,
            endIndent: 24,
          ),
          // Info section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: GPColor.blueLighter,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                SvgWidget(
                  Assets
                      .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_INFORMATIONMARK_CIRCLE_SVG,
                  width: 16,
                  height: 16,
                  color: GPColor.contentPrimary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Số cuộc họp sẽ được đặt lại về 10 vào 0h ngày 1/9/2025',
                    style: textStyle(GPTypography.bodyLarge),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            color: GPColor.lineTertiary,
            height: 1,
            thickness: 1,
          ),
          // Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SizedBox(
              width: double.infinity,
              child: GPWorkButton(
                title: 'Đăng ký nhận tư vấn',
                onTap: () {
                  Get.back();
                  
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String emoji, String boldText, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          emoji,
          style: textStyle(GPTypography.bodyMedium),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: RichText(
              text: TextSpan(children: [
            TextSpan(
              text: boldText,
              style: textStyle(GPTypography.bodyLarge)
                  ?.copyWith(fontWeight: FontWeight.w700),
            ),
            TextSpan(
              text: text,
              style: textStyle(GPTypography.bodyLarge),
            ),
          ])),
        ),
      ],
    );
  }
}