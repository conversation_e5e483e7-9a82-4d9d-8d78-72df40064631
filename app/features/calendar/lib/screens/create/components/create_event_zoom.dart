import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/widgets/gradient_box_border.dart';
import 'package:gp_feat_calendar/screens/create/components/popup/ai_assistant_info_bottomsheet.dart';
import 'package:gp_feat_calendar/screens/create/create_calendar_event_controller.dart';

import 'create_event_section.dart';

class CreateEventGGMeet extends GetView<CreateCalendarEventController> {
  const CreateEventGGMeet({
    super.key,
  });

  @override
  CreateCalendarEventController get controller =>
      Get.put(CreateCalendarEventController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ExpandableNotifier(
        controller: controller.meetExpanableController,
        child: Expandable(
          collapsed: const Row(),
          expanded: CreateEventSection(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        LocaleKeys.calendar_create_label_meet.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.copyWith(height: 1),
                      ),
                    ),
                    SizedBox(
                      width: 40,
                      height: 26,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: CupertinoSwitch(
                          value: controller.hasMeet.value,
                          onChanged: controller.onMeetChanged,
                          activeTrackColor: GPColor.functionAccentWorkSecondary,
                        ),
                      ),
                    ),
                  ],
                ).paddingSymmetric(horizontal: 16, vertical: 12),
                Obx(() => controller.hasMeet.value
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 16, right: 16, bottom: 12),
                            child: Row(
                              children: [
                                if (controller.googleEmail.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: SvgWidget(
                                      Assets
                                          .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_PERSON_CHECKMARK_SVG,
                                      width: 16,
                                      height: 16,
                                      color:
                                          GPColor.functionAccentWorkSecondary,
                                    ),
                                  ),
                                Expanded(
                                  child: Text(
                                    controller.googleEmail.isEmpty
                                        ? LocaleKeys
                                            .calendar_create_not_google_signed_in
                                            .tr
                                        : controller.googleEmail,
                                    maxLines: controller.googleEmail.isEmpty
                                        ? null
                                        : 1,
                                    overflow: controller.googleEmail.isEmpty
                                        ? null
                                        : TextOverflow.ellipsis,
                                    style: textStyle(GPTypography.bodyMedium)
                                        ?.copyWith(
                                            color: GPColor.contentSecondary),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // AI Assistant Section
                          // _buildAIAssistantSection(),
                          AIAssistantBox(
                            controller: controller,
                          )
                        ],
                      )
                    : const SizedBox())
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class AIAssistantBox extends StatelessWidget {
  const AIAssistantBox({super.key, required this.controller});

  final CreateCalendarEventController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: GradientBoxBorder(
              gradient: const LinearGradient(
            colors: [Color(0xFF4BAFF6), Color(0xFFFBA446)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          )),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Column(
            children: [
              Row(
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Image.asset(Assets
                              .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG),
                        ),
                      ),
                      Positioned(
                        right: -4,
                        bottom: -4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: GPColor.blue,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Bot',
                            style:
                                textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.blue,
                              height: 1.33,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                      child: Row(
                    children: [
                      Text(
                        "Trợ lý AI vào cuộc họp",
                        style: textStyle(GPTypography.headingSmall)
                            ?.copyWith(height: 1.2),
                      ),
                      InkWell(
                        onTap: () => _showAIAssistantInfoScreen(),
                        child: Padding(
                          padding: const EdgeInsets.all(4),
                          child: SvgWidget(
                            Assets
                                .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_INFORMATIONMARK_CIRCLE_SVG,
                            color: GPColor.contentSecondary,
                          ),
                        ),
                      )
                    ],
                  )),
                  const SizedBox(width: 12),
                  Obx(() {
                    final isRepeating = controller.isRepeatingEvent;
                    final switchWidget = SizedBox(
                      width: 40,
                      height: 26,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: CupertinoSwitch(
                          value: controller.hasAIAssistant.value,
                          onChanged: isRepeating
                              ? null
                              : controller.onAIAssistantChanged,
                          activeTrackColor: GPColor.functionAccentWorkSecondary,
                          inactiveThumbColor: controller.isRepeatingEvent
                              ? GPColor.contentSecondary
                              : null,
                        ),
                      ),
                    );

                    if (isRepeating) {
                      return InfoTooltipWidget(
                        controller: controller.aiAsistantTooltipController,
                        tooltipText:
                            'Không áp dụng Trợ lý cuộc họp với\ncác cuộc họp lặp lại',
                        direction: TooltipDirection.up,
                        arrowTipDistance: 20,
                        textAlign: TextAlign.left,
                        child: switchWidget,
                      );
                    }

                    return switchWidget;
                  }),
                ],
              ),
              const SizedBox(height: 18),
              Text(
                "🚀  Cho phép trợ lý cuộc họp tham gia và ghi chú nội dung quan trọng",
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    "🕛 Số lượt còn lại:",
                    style: textStyle(GPTypography.bodyMedium)
                        ?.copyWith(color: GPColor.contentSecondary),
                  ),
                  const SizedBox(width: 4),
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                          text: "3",
                          style: textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.functionAccentWorkSecondary)),
                      TextSpan(
                          text: "/5",
                          style: textStyle(GPTypography.bodyMedium)
                              ?.copyWith(color: GPColor.contentSecondary)),
                    ]),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                "⌛ Số cuộc họp sẽ được đặt lại vào 0h ngày ${DateTimeUtils.instance.getFirstDateNextMonth(DateTimeUtils.datePattern_1)}",
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAIAssistantInfoScreen() {
    Popup.instance.showBottomSheet(
      const AIAssistantInfoBottomSheet(),
      isScrollControlled: true,
      isDismissible: true,
    );
  }
}
